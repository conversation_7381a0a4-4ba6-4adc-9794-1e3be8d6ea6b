# Quick Start Guide - ApplySquad Supabase

Get your ApplySquad Supabase backend up and running in 5 minutes!

## 🚀 One-Command Setup

```bash
# Run the automated setup script
./setup.sh
```

This script will:
- ✅ Check prerequisites (Node.js, Docker)
- ✅ Install Supabase CLI
- ✅ Create environment file
- ✅ Start Supabase services
- ✅ Apply all migrations
- ✅ Load seed data

## 📋 Manual Setup (Alternative)

If you prefer manual setup:

### 1. Install Supabase CLI
```bash
npm install -g @supabase/cli
```

### 2. Start Services
```bash
supabase start
```

### 3. Access Your Backend
- **Supabase Studio**: http://127.0.0.1:54323
- **API Endpoint**: http://127.0.0.1:54321
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres

## 🎯 What You Get

### ✅ Complete Database Schema
- **12 tables** with relationships
- **40+ migrations** applied
- **Row Level Security** enabled
- **Automated timestamps**

### ✅ Authentication System
- Email/password authentication
- JWT token management
- User roles (customers, agents, admins)
- Secure access policies

### ✅ Business Features
- Customer management
- Agent assignment system
- Job application tracking
- Payment processing (Stripe)
- Document management
- Cover letter generation

### ✅ API Endpoints
All tables automatically get REST API endpoints:
- `GET /rest/v1/customers`
- `POST /rest/v1/jobs`
- `PUT /rest/v1/customer_jobs`
- And many more...

## 🔧 Essential Commands

### Database Management
```bash
# View service status
supabase status

# Reset database (reapply all migrations)
supabase db reset

# Generate new migration
./migration.sh diff migration_name

# Apply pending migrations
./migration.sh up
```

### Development
```bash
# View logs
supabase logs

# Stop services
supabase stop

# Restart services
supabase start
```

## 📊 Test Your Setup

### 1. Access Supabase Studio
Navigate to http://127.0.0.1:54323 and verify:
- All tables are present
- Sample data is loaded
- RLS policies are active

### 2. Test API Endpoints
```bash
# Get products (should return sample data)
curl http://127.0.0.1:54321/rest/v1/products \
  -H "apikey: YOUR_ANON_KEY"

# Check authentication endpoint
curl http://127.0.0.1:54321/auth/v1/signup \
  -H "apikey: YOUR_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 3. Verify Database Connection
```bash
# Connect to database directly
psql postgresql://postgres:postgres@127.0.0.1:54322/postgres

# List tables
\dt public.*

# Check a table
SELECT * FROM products LIMIT 5;
```

## 🔐 Security Configuration

### Default Settings
- **RLS enabled** on all tables
- **JWT expiry**: 1 hour
- **Email confirmations**: Disabled (dev mode)
- **Anonymous access**: Disabled

### API Keys
After starting Supabase, get your keys:
```bash
supabase status
```

Update your `.env.local` file with the actual keys.

## 🎨 Frontend Integration

### JavaScript/TypeScript
```javascript
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = 'http://127.0.0.1:54321'
const supabaseKey = 'your-anon-key'
const supabase = createClient(supabaseUrl, supabaseKey)

// Example: Get customers
const { data, error } = await supabase
  .from('customers')
  .select('*')
```

### React Example
```jsx
import { useEffect, useState } from 'react'
import { supabase } from './supabaseClient'

function CustomerList() {
  const [customers, setCustomers] = useState([])

  useEffect(() => {
    fetchCustomers()
  }, [])

  async function fetchCustomers() {
    const { data } = await supabase
      .from('customers')
      .select('*')
    setCustomers(data)
  }

  return (
    <div>
      {customers.map(customer => (
        <div key={customer.id}>{customer.name}</div>
      ))}
    </div>
  )
}
```

## 🚨 Troubleshooting

### Common Issues

**Port already in use**
```bash
supabase stop
supabase start
```

**Docker not running**
```bash
# Start Docker Desktop
docker --version
```

**Migration errors**
```bash
# Reset and reapply
supabase db reset
```

**Permission denied on scripts**
```bash
chmod +x setup.sh
chmod +x migration.sh
```

## 📚 Next Steps

1. **Read the documentation**
   - `README.md` - Complete project documentation
   - `PROJECT_SUMMARY.md` - Architecture overview
   - `MIGRATION_GUIDE.md` - Database management

2. **Explore the schema**
   - Open Supabase Studio
   - Browse tables and relationships
   - Test API endpoints

3. **Build your frontend**
   - Use the provided API endpoints
   - Implement authentication
   - Connect to your preferred framework

4. **Deploy to production**
   - Create Supabase project
   - Configure environment variables
   - Deploy migrations

## 🎉 You're Ready!

Your ApplySquad Supabase backend is now running with:
- ✅ Complete database schema
- ✅ Authentication system
- ✅ API endpoints
- ✅ Security policies
- ✅ Sample data

Start building your frontend application and connect to the API endpoints!

---

**Need help?** Check the troubleshooting section or refer to the complete documentation in `README.md`.
