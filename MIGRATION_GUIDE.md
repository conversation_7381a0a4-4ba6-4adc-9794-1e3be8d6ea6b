# Migration Guide - ApplySquad Supabase

This guide explains how to manage database migrations in the ApplySquad Supabase project.

## 📋 Current Migration Status

The project currently has **40+ migrations** that have been applied chronologically:

### Migration Timeline

| Date | Migration | Description |
|------|-----------|-------------|
| 2025-01-07 | `20250107140110_public.sql` | Initial schema setup with core tables |
| 2025-01-10 | `20250110143435_public.sql` | Additional table configurations |
| 2025-01-29 | `20250129203600_multi_agent.sql` | Multi-agent support implementation |
| 2025-02-04 | `20250204180237_public.sql` | Customer onboarding enhancements |
| 2025-03-11 | `20250311155806_public.sql` | Cover letter functionality |
| 2025-03-11 | `20250311163515_public.sql` | Timestamp triggers with moddatetime |
| 2025-05-13 | `20250513114555_public.sql` | Latest product code updates |

### What's Included

✅ **Core Tables**: customers, agents, jobs, payments, documents
✅ **Relationships**: Foreign keys and constraints
✅ **Security**: Row Level Security (RLS) policies
✅ **Indexes**: Performance optimization
✅ **Triggers**: Automated timestamp updates
✅ **Extensions**: moddatetime for updated_at fields
✅ **Enums**: Type safety for status fields
✅ **Permissions**: Proper role-based access

## 🔄 Migration Commands

### Basic Migration Operations

```bash
# View current migration status
supabase migration list

# Apply all pending migrations
supabase db reset

# Apply migrations without reset (production)
supabase migration up

# Rollback last migration (if supported)
supabase migration down
```

### Creating New Migrations

```bash
# Method 1: Generate from schema diff
supabase db diff -f migration_name

# Method 2: Create empty migration file
supabase migration new migration_name
```

### Migration Workflow

1. **Make Changes**: Modify schema in Supabase Studio or via SQL
2. **Generate Migration**: Use `supabase db diff -f migration_name`
3. **Review**: Check the generated SQL in `migrations/` directory
4. **Test**: Apply in local environment
5. **Deploy**: Push to production

## 🛠️ Working with Migrations

### Schema Changes

When making schema changes, always:

1. **Use the diff command** to generate migrations
2. **Review the generated SQL** before applying
3. **Test in local environment** first
4. **Backup production** before applying

### Example: Adding a New Column

```bash
# 1. Make changes in Supabase Studio or via SQL
# 2. Generate migration
supabase db diff -f add_user_preferences

# 3. Review the generated file
cat migrations/[timestamp]_add_user_preferences.sql

# 4. Apply locally
supabase db reset

# 5. If satisfied, deploy to production
```

### Example: Creating a New Table

```sql
-- Example migration content
create table "public"."user_preferences" (
    "id" uuid not null default gen_random_uuid(),
    "user_id" uuid not null,
    "preferences" jsonb,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now()
);

-- Add constraints
alter table "public"."user_preferences" 
add constraint "user_preferences_pkey" primary key ("id");

alter table "public"."user_preferences" 
add constraint "user_preferences_user_id_fkey" 
foreign key ("user_id") references auth.users("id") on delete cascade;

-- Enable RLS
alter table "public"."user_preferences" enable row level security;

-- Add policies
create policy "Users can manage their own preferences"
on "public"."user_preferences"
for all
to authenticated
using (auth.uid() = user_id)
with check (auth.uid() = user_id);

-- Add trigger for updated_at
create trigger user_preferences_updated_at 
before update on public.user_preferences 
for each row execute function moddatetime('updated_at');
```

## 🚨 Migration Best Practices

### DO's

✅ **Always backup** before applying migrations to production
✅ **Test migrations** in local environment first
✅ **Use descriptive names** for migration files
✅ **Include rollback plans** for complex changes
✅ **Review generated SQL** before applying
✅ **Apply migrations during low-traffic periods**
✅ **Monitor application** after migration deployment

### DON'Ts

❌ **Don't edit existing migration files** once applied
❌ **Don't skip migrations** or apply them out of order
❌ **Don't make breaking changes** without proper planning
❌ **Don't forget to update RLS policies** for new tables
❌ **Don't apply untested migrations** to production

## 🔧 Troubleshooting Migrations

### Common Issues

1. **Migration fails due to data conflicts**
   ```bash
   # Check data that might conflict
   # Fix data issues first, then retry migration
   ```

2. **Permission errors**
   ```bash
   # Ensure proper database permissions
   # Check if user has migration privileges
   ```

3. **Dependency issues**
   ```bash
   # Check if required extensions are installed
   # Verify foreign key references exist
   ```

### Recovery Strategies

1. **Rollback Strategy**
   ```bash
   # If migration fails, restore from backup
   # Apply corrected migration
   ```

2. **Data Migration Issues**
   ```bash
   # Create data migration scripts
   # Test with subset of data first
   ```

## 📊 Migration Monitoring

### After Migration Deployment

1. **Check application logs** for errors
2. **Monitor database performance** metrics
3. **Verify data integrity** with sample queries
4. **Test critical application features**
5. **Monitor user reports** for issues

### Rollback Plan

Always have a rollback plan:

1. **Database backup** before migration
2. **Application version** that works with old schema
3. **Rollback scripts** for complex changes
4. **Communication plan** for users if needed

## 🎯 Current Schema Summary

### Core Tables (11 tables)

1. **admins** - Administrative users
2. **agents** - Service providers
3. **agent_customers** - Agent-customer relationships
4. **customers** - Customer profiles
5. **customer_jobs** - Job applications
6. **customer_onboarding** - Customer preferences
7. **jobs** - Job listings
8. **documents** - File references
9. **job_documents** - Document-job relationships
10. **payments** - Payment records
11. **cover_letters** - Generated cover letters
12. **products** - Service packages

### Key Features

- **Multi-agent support** with proper relationships
- **Row Level Security** on all tables
- **Automated timestamps** with triggers
- **Type safety** with enums
- **File storage** integration
- **Payment processing** support

---

**Note**: This migration system is production-ready and follows Supabase best practices. All migrations have been tested and are safe to apply.
