#!/bin/bash

# ApplySquad Supabase Setup Script
# This script automates the setup process for the ApplySquad Supabase backend

set -e  # Exit on any error

echo "🚀 ApplySquad Supabase Setup"
echo "=============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if command_exists node; then
        NODE_VERSION=$(node --version)
        print_success "Node.js found: $NODE_VERSION"
    else
        print_error "Node.js is not installed. Please install Node.js v16 or higher."
        exit 1
    fi
    
    # Check npm
    if command_exists npm; then
        NPM_VERSION=$(npm --version)
        print_success "npm found: $NPM_VERSION"
    else
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Docker
    if command_exists docker; then
        DOCKER_VERSION=$(docker --version)
        print_success "Docker found: $DOCKER_VERSION"
    else
        print_warning "Docker is not installed. Supabase local development requires Docker."
        print_status "Please install Docker from https://www.docker.com/"
        read -p "Continue without Docker? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Install Supabase CLI
install_supabase_cli() {
    print_status "Checking Supabase CLI..."
    
    if command_exists supabase; then
        SUPABASE_VERSION=$(supabase --version)
        print_success "Supabase CLI found: $SUPABASE_VERSION"
    else
        print_status "Installing Supabase CLI..."
        npm install -g @supabase/cli
        
        if command_exists supabase; then
            print_success "Supabase CLI installed successfully"
        else
            print_error "Failed to install Supabase CLI"
            exit 1
        fi
    fi
}

# Create environment file
create_env_file() {
    print_status "Creating environment file..."
    
    if [ ! -f ".env.local" ]; then
        cat > .env.local << EOF
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI (for AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Stripe (for payments)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Email Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key_here

# SMS Configuration (optional)
SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN=your_twilio_token_here

# S3 Configuration (optional)
S3_HOST=your_s3_host_here
S3_REGION=your_s3_region_here
S3_ACCESS_KEY=your_s3_access_key_here
S3_SECRET_KEY=your_s3_secret_key_here
EOF
        print_success "Environment file created: .env.local"
        print_warning "Please update the environment variables with your actual values"
    else
        print_warning "Environment file already exists: .env.local"
    fi
}

# Start Supabase
start_supabase() {
    print_status "Starting Supabase local development environment..."
    
    # Check if Docker is running
    if command_exists docker; then
        if ! docker info >/dev/null 2>&1; then
            print_error "Docker is not running. Please start Docker and try again."
            exit 1
        fi
    fi
    
    # Start Supabase
    supabase start
    
    if [ $? -eq 0 ]; then
        print_success "Supabase started successfully!"
        echo ""
        print_status "Service URLs:"
        supabase status
    else
        print_error "Failed to start Supabase"
        exit 1
    fi
}

# Display next steps
show_next_steps() {
    echo ""
    echo "🎉 Setup Complete!"
    echo "=================="
    echo ""
    print_success "Your ApplySquad Supabase backend is ready!"
    echo ""
    echo "📋 Next Steps:"
    echo "1. Update environment variables in .env.local"
    echo "2. Access Supabase Studio: http://127.0.0.1:54323"
    echo "3. API endpoint: http://127.0.0.1:54321"
    echo "4. Database: postgresql://postgres:postgres@127.0.0.1:54322/postgres"
    echo ""
    echo "🔧 Useful Commands:"
    echo "• supabase status    - View service status"
    echo "• supabase stop      - Stop all services"
    echo "• supabase logs      - View logs"
    echo "• supabase db reset  - Reset database with migrations"
    echo ""
    echo "📚 Documentation:"
    echo "• README.md - Project documentation"
    echo "• https://supabase.com/docs - Supabase documentation"
    echo ""
}

# Main execution
main() {
    check_prerequisites
    install_supabase_cli
    create_env_file
    start_supabase
    show_next_steps
}

# Run main function
main "$@"
