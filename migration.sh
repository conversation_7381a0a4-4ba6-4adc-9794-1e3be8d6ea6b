#!/bin/bash

# ApplySquad Migration Helper Script
# This script provides utilities for managing database migrations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "ApplySquad Migration Helper"
    echo "=========================="
    echo ""
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  diff [name]     Generate migration from schema diff"
    echo "  new [name]      Create new empty migration"
    echo "  up              Apply pending migrations"
    echo "  reset           Reset database and apply all migrations"
    echo "  status          Show migration status"
    echo "  list            List all migrations"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 diff add_user_preferences"
    echo "  $0 new create_notifications_table"
    echo "  $0 up"
    echo "  $0 reset"
    echo ""
}

# Function to generate migration from diff
generate_diff() {
    local name=${1:-"public"}
    print_status "Generating migration from schema diff: $name"
    supabase db diff -f "$name"
    print_success "Migration generated successfully"
}

# Function to create new migration
create_new() {
    local name=${1:-"new_migration"}
    print_status "Creating new migration: $name"
    supabase migration new "$name"
    print_success "New migration created successfully"
}

# Function to apply migrations
apply_migrations() {
    print_status "Applying pending migrations..."
    supabase migration up
    print_success "Migrations applied successfully"
}

# Function to reset database
reset_database() {
    print_warning "This will reset the database and apply all migrations"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Resetting database..."
        supabase db reset
        print_success "Database reset completed"
    else
        print_status "Database reset cancelled"
    fi
}

# Function to show migration status
show_status() {
    print_status "Migration status:"
    supabase migration list
}

# Function to list migrations
list_migrations() {
    print_status "Available migrations:"
    ls -la migrations/ | grep -E "\.sql$" | awk '{print $9, $5, $6, $7, $8}'
}

# Main script logic
case "${1:-help}" in
    "diff")
        generate_diff "$2"
        ;;
    "new")
        create_new "$2"
        ;;
    "up")
        apply_migrations
        ;;
    "reset")
        reset_database
        ;;
    "status")
        show_status
        ;;
    "list")
        list_migrations
        ;;
    "help"|*)
        show_usage
        ;;
esac
