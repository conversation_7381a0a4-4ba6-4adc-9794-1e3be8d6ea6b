-- Seed data for products table
-- This file contains initial product data for the ApplySquad system

-- Create products table if it doesn't exist (this should already be created by migrations)
CREATE TABLE IF NOT EXISTS "public"."products" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "product_code" product_codes not null,
    "name" text not null,
    "description" text,
    "price_pennies" integer not null default 0,
    "active" boolean not null default true,
    CONSTRAINT "products_pkey" PRIMARY KEY ("id")
);

-- Insert sample product data
INSERT INTO "public"."products" (product_code, name, description, price_pennies, active) VALUES
('APPS_20', '20 Job Applications', 'Professional job application service for 20 positions', 2000, true),
('APPS_50', '50 Job Applications', 'Professional job application service for 50 positions', 4500, true),
('APPS_100', '100 Job Applications', 'Professional job application service for 100 positions', 8000, true),
('APPS_500', '500 Job Applications', 'Professional job application service for 500 positions', 35000, true),
('APPS_TRIAL_5', '5 Job Applications Trial', 'Trial package for 5 job applications', 500, true),
('NETWORK_20', '20 Network Connections', 'Professional networking service for 20 connections', 1500, true),
('NETWORK_80', '80 Network Connections', 'Professional networking service for 80 connections', 5000, true),
('OTHER', 'Other Services', 'Miscellaneous services and custom packages', 0, true)
ON CONFLICT (product_code) DO UPDATE SET
    name = EXCLUDED.name,
    description = EXCLUDED.description,
    price_pennies = EXCLUDED.price_pennies,
    active = EXCLUDED.active;

-- Grant permissions
GRANT SELECT ON "public"."products" TO authenticated;
GRANT SELECT ON "public"."products" TO anon;
GRANT ALL ON "public"."products" TO service_role;
